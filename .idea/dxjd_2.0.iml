<?xml version="1.0" encoding="UTF-8"?>
<module type="JAVA_MODULE" version="4">
  <component name="NewModuleRootManager" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <excludeFolder url="file://$MODULE_DIR$/dxjd/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/api/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/api/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/api/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/component_library/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/component_library/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/component_library/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/key_value_storage/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/key_value_storage/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/key_value_storage/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/test_one/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/test_one/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/test_one/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/user_repository/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/user_repository/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/user_repository/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/features/home/<USER>" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/features/home/<USER>" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/features/home/<USER>" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/features/login/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/features/login/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/features/login/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/features/mine/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/features/mine/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/features/mine/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/timing_repository/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/timing_repository/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/timing_repository/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/home_repository/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/home_repository/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/home_repository/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/domain_models/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/domain_models/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/domain_models/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/features/TEST/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/features/TEST/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/features/TEST/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/oss_repository/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/oss_repository/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/oss_repository/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/push_repository/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/push_repository/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/push_repository/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/beizi_ad/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/beizi_ad/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/beizi_ad/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/beizi_ad/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/beizi_ad/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/beizi_ad/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/native_camera/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/native_camera/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/native_camera/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/native_camera/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/native_camera/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/native_camera/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/take_photo_view/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/take_photo_view/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/take_photo_view/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/take_photo_view/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/take_photo_view/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/take_photo_view/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/video_cache/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/video_cache/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/video_cache/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/video_cache/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/video_cache/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/video_cache/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/quiz/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/quiz/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/quiz/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/timing/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/timing/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/timing/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/tools/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/tools/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/packages/tools/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/chewie-1.8.3/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/chewie-1.8.3/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/chewie-1.8.3/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/chewie-1.8.3/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/chewie-1.8.3/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/chewie-1.8.3/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/ali_auth/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/ali_auth/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/ali_auth/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/ali_auth/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/ali_auth/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/ali_auth/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/amap_flutter_location/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/amap_flutter_location/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/amap_flutter_location/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/amap_flutter_location/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/amap_flutter_location/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/amap_flutter_location/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/amap_flutter_map/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/amap_flutter_map/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/amap_flutter_map/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/amap_flutter_map/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/amap_flutter_map/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/amap_flutter_map/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/app_links/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/app_links/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/app_links/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/app_links/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/app_links/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/app_links/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/audioplayers_darwin/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/audioplayers_darwin/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/audioplayers_darwin/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/camera_avfoundation/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/camera_avfoundation/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/camera_avfoundation/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/camera_avfoundation/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/camera_avfoundation/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/camera_avfoundation/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/camerawesome/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/camerawesome/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/camerawesome/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/camerawesome/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/camerawesome/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/camerawesome/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/connectivity/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/connectivity/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/connectivity/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/connectivity/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/connectivity/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/connectivity/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/connectivity_plus/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/connectivity_plus/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/connectivity_plus/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/connectivity_plus/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/connectivity_plus/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/connectivity_plus/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/device_info_plus/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/device_info_plus/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/device_info_plus/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/device_info_plus/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/device_info_plus/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/device_info_plus/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/flutter_barrage_craft/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/flutter_barrage_craft/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/flutter_barrage_craft/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/flutter_barrage_craft/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/flutter_barrage_craft/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/flutter_barrage_craft/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/flutter_image_compress_common/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/flutter_image_compress_common/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/flutter_image_compress_common/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/flutter_inapp_purchase/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/flutter_inapp_purchase/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/flutter_inapp_purchase/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/flutter_inapp_purchase/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/flutter_inapp_purchase/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/flutter_inapp_purchase/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/flutter_keyboard_visibility/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/flutter_keyboard_visibility/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/flutter_keyboard_visibility/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/flutter_keyboard_visibility/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/flutter_keyboard_visibility/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/flutter_keyboard_visibility/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/flutter_native_image/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/flutter_native_image/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/flutter_native_image/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/flutter_native_image/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/flutter_native_image/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/flutter_native_image/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/flutter_secure_storage/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/flutter_secure_storage/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/flutter_secure_storage/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/flutter_secure_storage/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/flutter_secure_storage/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/flutter_secure_storage/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/fluttertoast/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/fluttertoast/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/fluttertoast/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/fluttertoast/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/fluttertoast/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/fluttertoast/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/fluwx/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/fluwx/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/fluwx/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/image_editor_common/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/image_editor_common/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/image_editor_common/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/image_gallery_saver/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/image_gallery_saver/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/image_gallery_saver/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/image_gallery_saver/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/image_gallery_saver/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/image_gallery_saver/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/image_picker_ios/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/image_picker_ios/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/image_picker_ios/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/image_picker_ios/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/image_picker_ios/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/image_picker_ios/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/jpush_flutter/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/jpush_flutter/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/jpush_flutter/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/jpush_flutter/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/jpush_flutter/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/jpush_flutter/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/mop/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/mop/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/mop/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/mop/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/mop/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/mop/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/package_info_plus/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/package_info_plus/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/package_info_plus/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/package_info_plus/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/package_info_plus/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/package_info_plus/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/path_provider_foundation/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/path_provider_foundation/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/path_provider_foundation/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/path_provider_foundation/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/path_provider_foundation/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/path_provider_foundation/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/permission_handler_apple/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/permission_handler_apple/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/permission_handler_apple/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/permission_handler_apple/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/permission_handler_apple/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/permission_handler_apple/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/scan/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/scan/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/scan/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/scan/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/scan/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/scan/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/shared_preferences_foundation/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/shared_preferences_foundation/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/shared_preferences_foundation/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/shared_preferences_foundation/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/shared_preferences_foundation/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/shared_preferences_foundation/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/sign_in_with_apple/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/sign_in_with_apple/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/sign_in_with_apple/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/sign_in_with_apple/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/sign_in_with_apple/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/sign_in_with_apple/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/sqflite/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/sqflite/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/sqflite/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/sqflite/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/sqflite/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/sqflite/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/sqflite_sqlcipher/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/sqflite_sqlcipher/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/sqflite_sqlcipher/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/sqflite_sqlcipher/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/sqflite_sqlcipher/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/sqflite_sqlcipher/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/url_launcher_ios/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/url_launcher_ios/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/url_launcher_ios/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/url_launcher_ios/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/url_launcher_ios/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/url_launcher_ios/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/video_player_avfoundation/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/video_player_avfoundation/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/video_player_avfoundation/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/video_player_avfoundation/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/video_player_avfoundation/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/video_player_avfoundation/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/wakelock_plus/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/wakelock_plus/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/wakelock_plus/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/wakelock_plus/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/wakelock_plus/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/wakelock_plus/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/webview_flutter_wkwebview/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/webview_flutter_wkwebview/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/webview_flutter_wkwebview/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/webview_flutter_wkwebview/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/webview_flutter_wkwebview/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/ios/.symlinks/plugins/webview_flutter_wkwebview/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/beizi_ad_sdk/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/beizi_ad_sdk/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/beizi_ad_sdk/build" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/beizi_ad_sdk/example/.dart_tool" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/beizi_ad_sdk/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/dxjd/plugins/beizi_ad_sdk/example/build" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Dart SDK" level="project" />
    <orderEntry type="library" name="Dart Packages" level="project" />
  </component>
</module>