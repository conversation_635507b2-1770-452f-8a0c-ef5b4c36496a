<component name="libraryTable">
  <library name="Dart Packages" type="DartPackagesLibraryType">
    <properties>
      <option name="packageNameToDirsMap">
        <entry key="_fe_analyzer_shared">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/_fe_analyzer_shared-67.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="ali_auth">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/ali_auth-1.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="amap_flutter_base">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/amap_flutter_base-3.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="amap_flutter_location">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/amap_flutter_location-3.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="amap_flutter_map">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/amap_flutter_map-3.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="analyzer">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/analyzer-6.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="app_links">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/app_links-6.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="app_links_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/app_links_linux-1.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="app_links_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/app_links_platform_interface-2.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="app_links_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/app_links_web-1.0.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="archive">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/archive-4.0.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="args">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/args-2.7.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="asn1lib">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/asn1lib-1.5.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="async">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/async-2.11.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="audioplayers">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/audioplayers-1.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="audioplayers_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/audioplayers_android-1.1.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="audioplayers_darwin">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/audioplayers_darwin-1.0.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="audioplayers_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/audioplayers_linux-1.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="audioplayers_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/audioplayers_platform_interface-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="audioplayers_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/audioplayers_web-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="audioplayers_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/audioplayers_windows-1.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="azlistview">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/azlistview-2.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="bloc">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/bloc-9.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="boolean_selector">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/boolean_selector-2.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="build">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="build_config">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build_config-1.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="build_daemon">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build_daemon-4.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="build_resolvers">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build_resolvers-2.4.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="build_runner">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build_runner-2.4.9/lib" />
            </list>
          </value>
        </entry>
        <entry key="build_runner_core">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build_runner_core-7.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="built_collection">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/built_collection-5.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="built_value">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/built_value-8.9.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="cached_network_image">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cached_network_image-3.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="cached_network_image_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cached_network_image_platform_interface-4.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="cached_network_image_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cached_network_image_web-1.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="camera">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/camera-0.10.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="camera_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/camera_android-0.10.9+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="camera_avfoundation">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/camera_avfoundation-0.9.17+5/lib" />
            </list>
          </value>
        </entry>
        <entry key="camera_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/camera_platform_interface-2.8.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="camera_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/camera_web-0.3.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="camerawesome">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/camerawesome-0.3.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="characters">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/characters-1.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="checked_yaml">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/checked_yaml-2.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="cli_util">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cli_util-0.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="clock">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/clock-1.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="code_builder">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/code_builder-4.10.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="collection">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/collection-1.18.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="common_utils">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/common_utils-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="connectivity">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/connectivity-3.0.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="connectivity_for_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/connectivity_for_web-0.4.0+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="connectivity_macos">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/connectivity_macos-0.2.1+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="connectivity_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/connectivity_platform_interface-2.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="connectivity_plus">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus-6.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="connectivity_plus_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus_platform_interface-2.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="convert">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/convert-3.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="crop_your_image">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/crop_your_image-1.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="cross_file">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cross_file-0.3.4+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="crypto">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/crypto-3.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="csslib">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/csslib-0.17.3/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/csslib-1.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="cupertino_icons">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cupertino_icons-1.0.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="dart_style">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/dart_style-2.3.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="dbus">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/dbus-0.7.11/lib" />
            </list>
          </value>
        </entry>
        <entry key="decimal">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/decimal-2.3.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="device_info_plus">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-10.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="device_info_plus_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/device_info_plus_platform_interface-7.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="dio">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/dio-5.8.0+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="dio_web_adapter">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/dio_web_adapter-2.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="easy_refresh">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/easy_refresh-3.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="easy_rich_text">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/easy_rich_text-2.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="encrypt">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/encrypt-5.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="equatable">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/equatable-2.0.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="event_bus">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/event_bus-2.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="extended_image">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/extended_image-6.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="extended_image_library">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/extended_image_library-3.5.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="fake_async">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/fake_async-1.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="ffi">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/ffi-2.1.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="file">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/file-7.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="file_selector_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/file_selector_linux-0.9.3+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="file_selector_macos">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/file_selector_macos-0.9.4+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="file_selector_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/file_selector_platform_interface-2.6.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="file_selector_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/file_selector_windows-0.9.3+3/lib" />
            </list>
          </value>
        </entry>
        <entry key="fixnum">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/fixnum-1.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flukit">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flukit-3.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter">
          <value>
            <list>
              <option value="$USER_HOME$/fvm/versions/3.19.1/packages/flutter/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_barrage_craft">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_barrage_craft-1.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_bloc">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_bloc-9.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_cache_manager">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_cache_manager-3.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_carousel_widget">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_carousel_widget-2.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_easyloading">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_easyloading-3.0.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_easyrefresh">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_easyrefresh-2.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_html">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_html-3.0.0-beta.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_image_compress">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_image_compress-2.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_image_compress_common">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_image_compress_common-1.0.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_image_compress_macos">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_image_compress_macos-1.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_image_compress_ohos">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_image_compress_ohos-0.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_image_compress_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_image_compress_platform_interface-1.0.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_image_compress_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_image_compress_web-0.1.4+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_inapp_purchase">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_inapp_purchase-5.4.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_keyboard_visibility">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_keyboard_visibility-6.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_keyboard_visibility_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_keyboard_visibility_linux-1.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_keyboard_visibility_macos">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_keyboard_visibility_macos-1.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_keyboard_visibility_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_keyboard_visibility_platform_interface-2.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_keyboard_visibility_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_keyboard_visibility_web-2.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_keyboard_visibility_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_keyboard_visibility_windows-1.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_launcher_icons">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_launcher_icons-0.13.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_lints">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_lints-3.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_localizations">
          <value>
            <list>
              <option value="$USER_HOME$/fvm/versions/3.19.1/packages/flutter_localizations/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_native_image">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_native_image-0.0.6+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_oss_aliyun">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_oss_aliyun-6.4.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_plugin_android_lifecycle">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_plugin_android_lifecycle-2.0.19/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_screenutil">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_screenutil-5.9.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_secure_storage">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_secure_storage-4.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_spinkit">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_spinkit-5.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_staggered_grid_view">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_staggered_grid_view-0.7.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_switch">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_switch-0.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_test">
          <value>
            <list>
              <option value="$USER_HOME$/fvm/versions/3.19.1/packages/flutter_test/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_web_plugins">
          <value>
            <list>
              <option value="$USER_HOME$/fvm/versions/3.19.1/packages/flutter_web_plugins/lib" />
            </list>
          </value>
        </entry>
        <entry key="fluttertoast">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/fluttertoast-8.2.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="fluwx">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/fluwx-4.6.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="freezed">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/freezed-2.5.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="freezed_annotation">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/freezed_annotation-2.4.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="frontend_server_client">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/frontend_server_client-4.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="get">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/get-4.7.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="glob">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/glob-2.1.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="graphs">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/graphs-2.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="gtk">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/gtk-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="hive">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/hive-2.2.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="hive_flutter">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/hive_flutter-1.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="hive_generator">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/hive_generator-2.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="html">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/html-0.15.4/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/html-0.15.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="http">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/http-0.13.6/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/http-1.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="http_client_helper">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/http_client_helper-2.0.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="http_multi_server">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/http_multi_server-3.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="http_parser">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/http_parser-4.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="image">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image-4.5.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_editor">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_editor-1.6.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_editor_common">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_editor_common-1.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_editor_ohos">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_editor_ohos-0.0.9/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_editor_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_editor_platform_interface-1.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_gallery_saver">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_gallery_saver-2.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_picker-1.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_picker_android-0.8.12+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_for_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_picker_for_web-3.0.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_ios">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_picker_linux-0.2.1+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_macos">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_picker_macos-0.2.1+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_picker_platform_interface-2.10.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_picker_windows-0.2.1+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="intl">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/intl-0.18.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="io">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/io-1.0.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="jpush_flutter">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/jpush_flutter-3.2.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="js">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/js-0.6.7/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/js-0.7.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="json_annotation">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/json_annotation-4.9.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="json_serializable">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/json_serializable-6.8.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="leak_tracker">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/leak_tracker-10.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="leak_tracker_flutter_testing">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/leak_tracker_flutter_testing-2.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="leak_tracker_testing">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/leak_tracker_testing-2.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="lints">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/lints-3.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="list_counter">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/list_counter-1.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="logging">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/logging-1.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="lpinyin">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/lpinyin-2.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="matcher">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/matcher-0.12.16+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="material_color_utilities">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/material_color_utilities-0.8.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="meta">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/meta-1.11.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="mime">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/mime-1.0.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="mop">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/git/mop-flutter-sdk-312cf58fbc9b5e4c04fa3877b4051650aed92770//lib" />
            </list>
          </value>
        </entry>
        <entry key="nested">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/nested-1.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="nm">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/nm-0.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="octo_image">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/octo_image-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="package_config">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/package_config-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="package_info_plus">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-7.0.0/lib" />
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-8.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="package_info_plus_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/package_info_plus_platform_interface-3.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="path">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path-1.9.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_drawing">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_drawing-1.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_parsing">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_parsing-1.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider-2.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_android-2.2.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_foundation">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_linux-2.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_platform_interface-2.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_windows-2.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="percent_indicator">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/percent_indicator-4.2.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler-10.4.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_android-10.3.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_apple">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_platform_interface-3.12.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_windows-0.1.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="petitparser">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/petitparser-6.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="photo_view">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/photo_view-0.14.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="platform">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/platform-3.1.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="plugin_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/plugin_platform_interface-2.1.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="pointycastle">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/pointycastle-3.9.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="pool">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/pool-1.5.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="posix">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/posix-6.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="pretty_dio_logger">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/pretty_dio_logger-1.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="provider">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/provider-6.1.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="pub_semver">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/pub_semver-2.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="pubspec_parse">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/pubspec_parse-1.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="qr">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/qr-3.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="qr_flutter">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/qr_flutter-4.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="queue">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/queue-3.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="rational">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/rational-2.2.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="routemaster">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/routemaster-1.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="rxdart">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/rxdart-0.27.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="scan">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/scan-1.6.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="scrollable_positioned_list">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/scrollable_positioned_list-0.2.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="scrollview_observer">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/scrollview_observer-1.26.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences-2.2.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_android-2.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_foundation">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.5.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_linux-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_platform_interface-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_web-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_windows-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="shelf">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shelf-1.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="shelf_web_socket">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shelf_web_socket-1.0.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="sign_in_with_apple">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sign_in_with_apple-6.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="sign_in_with_apple_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sign_in_with_apple_platform_interface-1.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="sign_in_with_apple_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sign_in_with_apple_web-2.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="sky_engine">
          <value>
            <list>
              <option value="$USER_HOME$/fvm/versions/3.19.1/bin/cache/pkg/sky_engine/lib" />
            </list>
          </value>
        </entry>
        <entry key="source_gen">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/source_gen-1.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="source_helper">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/source_helper-1.3.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="source_span">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/source_span-1.10.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="sqflite">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sqflite-2.3.3+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="sqflite_common">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sqflite_common-2.5.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="sqflite_sqlcipher">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sqflite_sqlcipher-2.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="stack_trace">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/stack_trace-1.11.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="stop_watch_timer">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/stop_watch_timer-1.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="stream_channel">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/stream_channel-2.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="stream_transform">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/stream_transform-2.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="string_scanner">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/string_scanner-1.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="synchronized">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/synchronized-3.1.0+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="term_glyph">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/term_glyph-1.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="test_api">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/test_api-0.6.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="timing">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/timing-1.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="typed_data">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/typed_data-1.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher-6.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_android-6.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_ios">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_linux-3.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_macos">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_macos-3.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_platform_interface-2.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_web-2.3.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_windows-3.1.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="uuid">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/uuid-3.0.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_math">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/vector_math-2.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="video_player">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/video_player-2.9.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="video_player_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/video_player_android-2.4.14/lib" />
            </list>
          </value>
        </entry>
        <entry key="video_player_avfoundation">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/video_player_avfoundation-2.6.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="video_player_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/video_player_platform_interface-6.2.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="video_player_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/video_player_web-2.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="vm_service">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/vm_service-13.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="wakelock_plus">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/wakelock_plus-1.2.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="wakelock_plus_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/wakelock_plus_platform_interface-1.2.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="watcher">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/watcher-1.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/web-0.5.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="web_socket_channel">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/web_socket_channel-2.4.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="webview_flutter">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/webview_flutter-3.0.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="webview_flutter_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_android-2.10.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="webview_flutter_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_platform_interface-1.9.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="webview_flutter_wkwebview">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-2.9.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="win32">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/win32-5.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="win32_registry">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/win32_registry-1.1.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="xdg_directories">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/xdg_directories-1.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="xml">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/xml-6.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="yaml">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/yaml-3.1.2/lib" />
            </list>
          </value>
        </entry>
      </option>
    </properties>
    <CLASSES>
      <root url="file://$USER_HOME$/.pub-cache/git/mop-flutter-sdk-312cf58fbc9b5e4c04fa3877b4051650aed92770//lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/_fe_analyzer_shared-67.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/ali_auth-1.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/amap_flutter_base-3.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/amap_flutter_location-3.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/amap_flutter_map-3.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/analyzer-6.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/app_links-6.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/app_links_linux-1.0.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/app_links_platform_interface-2.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/app_links_web-1.0.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/archive-4.0.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/args-2.7.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/asn1lib-1.5.8/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/async-2.11.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/audioplayers-1.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/audioplayers_android-1.1.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/audioplayers_darwin-1.0.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/audioplayers_linux-1.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/audioplayers_platform_interface-2.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/audioplayers_web-2.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/audioplayers_windows-1.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/azlistview-2.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/bloc-9.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/boolean_selector-2.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build_config-1.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build_daemon-4.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build_resolvers-2.4.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build_runner-2.4.9/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/build_runner_core-7.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/built_collection-5.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/built_value-8.9.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cached_network_image-3.3.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cached_network_image_platform_interface-4.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cached_network_image_web-1.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/camera-0.10.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/camera_android-0.10.9+2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/camera_avfoundation-0.9.17+5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/camera_platform_interface-2.8.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/camera_web-0.3.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/camerawesome-0.3.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/characters-1.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/checked_yaml-2.0.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cli_util-0.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/clock-1.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/code_builder-4.10.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/collection-1.18.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/common_utils-2.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/connectivity-3.0.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/connectivity_for_web-0.4.0+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/connectivity_macos-0.2.1+2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/connectivity_platform_interface-2.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus-6.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/connectivity_plus_platform_interface-2.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/convert-3.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/crop_your_image-1.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cross_file-0.3.4+2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/crypto-3.0.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/csslib-0.17.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/csslib-1.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/cupertino_icons-1.0.8/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/dart_style-2.3.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/dbus-0.7.11/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/decimal-2.3.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-10.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/device_info_plus_platform_interface-7.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/dio-5.8.0+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/dio_web_adapter-2.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/easy_refresh-3.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/easy_rich_text-2.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/encrypt-5.0.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/equatable-2.0.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/event_bus-2.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/extended_image-6.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/extended_image_library-3.5.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/fake_async-1.3.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/ffi-2.1.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/file-7.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/file_selector_linux-0.9.3+2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/file_selector_macos-0.9.4+2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/file_selector_platform_interface-2.6.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/file_selector_windows-0.9.3+3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/fixnum-1.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flukit-3.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_barrage_craft-1.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_bloc-9.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_cache_manager-3.3.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_carousel_widget-2.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_easyloading-3.0.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_easyrefresh-2.2.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_html-3.0.0-beta.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_image_compress-2.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_image_compress_common-1.0.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_image_compress_macos-1.0.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_image_compress_ohos-0.0.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_image_compress_platform_interface-1.0.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_image_compress_web-0.1.4+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_inapp_purchase-5.4.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_keyboard_visibility-6.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_keyboard_visibility_linux-1.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_keyboard_visibility_macos-1.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_keyboard_visibility_platform_interface-2.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_keyboard_visibility_web-2.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_keyboard_visibility_windows-1.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_launcher_icons-0.13.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_lints-3.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_native_image-0.0.6+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_oss_aliyun-6.4.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_plugin_android_lifecycle-2.0.19/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_screenutil-5.9.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_secure_storage-4.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_spinkit-5.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_staggered_grid_view-0.7.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/flutter_switch-0.3.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/fluttertoast-8.2.8/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/fluwx-4.6.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/freezed-2.5.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/freezed_annotation-2.4.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/frontend_server_client-4.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/get-4.7.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/glob-2.1.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/graphs-2.3.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/gtk-2.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/hive-2.2.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/hive_flutter-1.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/hive_generator-2.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/html-0.15.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/html-0.15.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/http-0.13.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/http-1.2.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/http_client_helper-2.0.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/http_multi_server-3.2.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/http_parser-4.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image-4.5.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_editor-1.6.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_editor_common-1.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_editor_ohos-0.0.9/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_editor_platform_interface-1.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_gallery_saver-2.0.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_picker-1.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_picker_android-0.8.12+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_picker_for_web-3.0.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_picker_linux-0.2.1+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_picker_macos-0.2.1+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_picker_platform_interface-2.10.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/image_picker_windows-0.2.1+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/intl-0.18.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/io-1.0.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/jpush_flutter-3.2.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/js-0.6.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/js-0.7.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/json_annotation-4.9.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/json_serializable-6.8.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/leak_tracker-10.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/leak_tracker_flutter_testing-2.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/leak_tracker_testing-2.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/lints-3.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/list_counter-1.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/logging-1.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/lpinyin-2.0.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/matcher-0.12.16+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/material_color_utilities-0.8.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/meta-1.11.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/mime-1.0.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/nested-1.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/nm-0.5.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/octo_image-2.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/package_config-2.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-7.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-8.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/package_info_plus_platform_interface-3.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path-1.9.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_drawing-1.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_parsing-1.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider-2.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_android-2.2.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_linux-2.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_platform_interface-2.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/path_provider_windows-2.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/percent_indicator-4.2.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler-10.4.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_android-10.3.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_platform_interface-3.12.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_windows-0.1.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/petitparser-6.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/photo_view-0.14.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/platform-3.1.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/plugin_platform_interface-2.1.8/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/pointycastle-3.9.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/pool-1.5.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/posix-6.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/pretty_dio_logger-1.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/provider-6.1.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/pub_semver-2.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/pubspec_parse-1.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/qr-3.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/qr_flutter-4.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/queue-3.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/rational-2.2.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/routemaster-1.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/rxdart-0.27.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/scan-1.6.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/scrollable_positioned_list-0.2.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/scrollview_observer-1.26.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences-2.2.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_android-2.2.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.5.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_linux-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_platform_interface-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_web-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_windows-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shelf-1.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/shelf_web_socket-1.0.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sign_in_with_apple-6.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sign_in_with_apple_platform_interface-1.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sign_in_with_apple_web-2.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/source_gen-1.5.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/source_helper-1.3.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/source_span-1.10.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sqflite-2.3.3+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sqflite_common-2.5.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/sqflite_sqlcipher-2.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/stack_trace-1.11.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/stop_watch_timer-1.5.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/stream_channel-2.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/stream_transform-2.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/string_scanner-1.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/synchronized-3.1.0+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/term_glyph-1.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/test_api-0.6.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/timing-1.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/typed_data-1.3.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher-6.3.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_android-6.3.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_linux-3.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_macos-3.2.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_platform_interface-2.3.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_web-2.3.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/url_launcher_windows-3.1.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/uuid-3.0.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/vector_math-2.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/video_player-2.9.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/video_player_android-2.4.14/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/video_player_avfoundation-2.6.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/video_player_platform_interface-6.2.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/video_player_web-2.3.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/vm_service-13.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/wakelock_plus-1.2.8/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/wakelock_plus_platform_interface-1.2.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/watcher-1.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/web-0.5.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/web_socket_channel-2.4.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/webview_flutter-3.0.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_android-2.10.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_platform_interface-1.9.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-2.9.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/win32-5.5.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/win32_registry-1.1.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/xdg_directories-1.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/xml-6.5.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.flutter-io.cn/yaml-3.1.2/lib" />
      <root url="file://$USER_HOME$/fvm/versions/3.19.1/bin/cache/pkg/sky_engine/lib" />
      <root url="file://$USER_HOME$/fvm/versions/3.19.1/packages/flutter/lib" />
      <root url="file://$USER_HOME$/fvm/versions/3.19.1/packages/flutter_localizations/lib" />
      <root url="file://$USER_HOME$/fvm/versions/3.19.1/packages/flutter_test/lib" />
      <root url="file://$USER_HOME$/fvm/versions/3.19.1/packages/flutter_web_plugins/lib" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>