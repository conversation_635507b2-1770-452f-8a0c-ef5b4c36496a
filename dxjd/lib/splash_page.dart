import 'package:component_library/component_library.dart';
import 'package:dxjd/mainController.dart';
import 'package:flutter/material.dart';
import 'package:key_value_storage/key_value_storage.dart';
import 'package:login/login.dart';
import 'package:tools/tools.dart';
import 'package:get/get.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  late MainController mainController;
  // final _keyValueStorage = KeyValueStorage();
  // late final Api _api = Api(
  //   userTokenSupplier: () => _userRepository.getUserToken(),
  // );
  // late final _userRepository = UserRepository(
  //   remoteApi: _api,
  //   noSqlStorage: _keyValueStorage,
  // );
  String _splashImage = 'img_qidong_zuoti';


  @override
  void initState() {
    mainController = Get.put(MainController(), permanent: true);
    // _refreshToken();

    PreferencesService().getBool('fist_run').then((value) {
      if (value == null) {
        _firstRunChange();
        // PreferencesService().setBool('fist_run', true);
      }
    });

    super.initState();
  }

  _firstRunChange() {
    Future.delayed(Duration(milliseconds: 1000), () {
      setState(() {
        _splashImage = 'img_qidong_jishi';
      });
    });
  }
  //
  // _refreshToken() async {
  //   await _userRepository.renewalToken();
  // }


  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    UIUtils.initScreenParam(context);
    return Scaffold(
      body: assImg(
          img: _splashImage,
          fit: BoxFit.fill,
          w: MediaQuery.of(context).size.width,
          h: double.infinity),
    );
  }
}


class AdPlaceholder extends StatelessWidget {
  const AdPlaceholder({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: assImg(
          img: 'img_qidong_zuoti',
          fit: BoxFit.fill,
          w: MediaQuery.of(context).size.width,
          h: double.infinity),
    );
  }
}

