import 'dart:async';
import 'dart:io';

import 'package:component_library/component_library.dart';
import 'package:domain_models/domain_models.dart';
import 'package:dxjd/bottom_tabbar_controller.dart';
import 'package:dxjd/mainController.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:home/home.dart';
import 'package:key_value_storage/key_value_storage.dart';
import 'package:login/login.dart';
import 'package:mine/mine.dart';
import 'package:timing/timing.dart';
import 'package:tools/tools.dart';
import 'l10n/app_localizations.dart';

class TabContainerScreen extends StatefulWidget {
  TabContainerScreen({
    Key? key,
  }) : super(key: key);

  @override
  State<TabContainerScreen> createState() => TabContainerScreenState();
}

class TabContainerScreenState extends State<TabContainerScreen>
    with WidgetsBindingObserver {
  late MainController mainController;
  bool isCanPopTip = true;
  static late Key key;
  @override
  void initState() {
    mainController = Get.find<MainController>();
    key = UniqueKey();
    debugPrint('initState:----TabContainerScreen');
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    // 计时悬浮窗监听
    _addFloatingEndTimingButton();
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // _showFloatingButton();/
    });
    // _showFloatingButton();
  }

  // static OverlayEntry? overlayEntry;

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    TimingHelper.remoteTrainValueNotifier?.removeListener(() {});
    super.dispose();
  }

  // void _showFloatingButton() {
  //   overlayEntry = OverlayEntry(
  //     builder: (context) => Positioned(
  //         // right: 16.0,
  //         // bottom: 16.0,
  //         child: assImg(
  //             img: 'img_qidong_zuoti',
  //             fit: BoxFit.fill,
  //             w: MediaQuery.of(context).size.width,
  //             h: double.infinity)),
  //   );
  // }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    debugPrint('didChangeDependencies:--');
  }

  @override
  void didUpdateWidget(covariant TabContainerScreen oldWidget) {
    super.didUpdateWidget(oldWidget);
    debugPrint('didUpdateWidget:----');
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        if (didPop) {
          return;
        } else {
          if (isCanPopTip) {
            EasyLoading.showToast("再试一次关闭应用");
            isCanPopTip = false;
            Future.delayed(const Duration(seconds: 3), () {
              isCanPopTip = true;
            });
          } else {
            exit(0);
          }
        }
      },
      child: UIUtils.statusBarDarkWidget(GetBuilder<BottomTabBarController>(
          tag: TabContainerScreenState.key.toString(),
          init: BottomTabBarController(),
          builder: (logic) {
            return Scaffold(
              extendBody: true,
              body: PageView(
                //要点1
                physics: const NeverScrollableScrollPhysics(),
                //禁止页面左右滑动切换
                controller: logic.pageController,
                //回调函数
                children: [
                  // ActivityPage(),
                  ExaminationPage(),
                  const SizedBox(),
                  MinePage(),
                  // MinePage()
                ],
              ),
              bottomNavigationBar: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  // borderRadius: BorderRadius.only(
                  //   topLeft: Radius.circular(20.r),
                  //   topRight: Radius.circular(20.r),
                  // ),
                  border: Border(top: BorderSide(color: const Color(0xFFF1F1F1),width: 1.h))
                ),
                child: BottomNavigationBar(
                  type: BottomNavigationBarType.fixed,
                  currentIndex: logic.selectIndex,
                  onTap: (int index) async {
                    if (TimingHelper.isRemoteTrain) {
                      Toast.show(TimingConstant.TheoryTimingTip);
                      return;
                    }
                    if (index == 2) {
                      if (!MainController.isLoginIntercept()) {
                        return;
                      }
                      BuryingPointUtils.instance.addPoint(
                          buryingPointList: BuryingPointList(
                              eventType: 3,
                              entranceType: 41,
                              action: 1,
                              browseDuration: 0));
                    }
                    if (index != 1) {
                      logic.pageController.jumpToPage(index);
                      logic.updateSelectIndex(index);
                      if (Get.isRegistered<ExaminationController>(
                          tag: ExaminationPageState.key.toString())) {
                        Get.find<ExaminationController>(
                                tag: ExaminationPageState.key.toString())
                            .update();
                      }
                    }
                    if (index == 0) {
                      BuryingPointUtils.instance.addPoint(
                          buryingPointList: BuryingPointList(
                              eventType: 3,
                              entranceType: 39,
                              action: 1,
                              browseDuration: 0));
                      mainController.userRepository.setVipProduct();
                      mainController.userRepository.queryUserInfo(
                          showLoading: false, isNeedUpdateInfo: false);
                      final String token =
                          await mainController.userRepository.getUserToken() ??
                              "";
                      if (token.isNotEmpty) {
                        try {
                          await mainController.homeRepository
                                  .getUnReadMessage() ??
                              "";
                        } catch (e) {}
                      }
                    } else if (index == 1) {
                      logic.setFindRedPoint();
                      String shopPath = '';
                      if (!MainController.isExistToken) {
                        shopPath = '/pages/entrance/entrance';
                      } else {
                        shopPath = '/pages/index/index';
                      }
                      BuryingPointUtils.instance.addPoint(
                          buryingPointList: BuryingPointList(
                              eventType: 3,
                              entranceType: 40,
                              action: 1,
                              browseDuration: 0));
                      JumpSmallProgramUtils.jump(
                          shopPath, "fc2259710004813765");
                      return;
                    }
                  },
                  elevation: 0,
                  selectedLabelStyle: TextStyle(
                      fontSize: 10.sp, fontFamily: 'PingFangSC-Medium'),
                  unselectedLabelStyle: TextStyle(
                      fontSize: 10.sp, fontFamily: 'PingFangSC-Medium'),
                  selectedItemColor: const Color(0xff007AFF),
                  unselectedItemColor: const Color(0xffB7B7B7),
                  backgroundColor: Colors.transparent,
                  items: [
                    // BottomNavigationBarItem(
                    //     label: l10n.activeBottomNavigationBarItemLabel,
                    //     icon: Image(
                    //       image: AssetImage(
                    //           "assets/home_img/tab_icon_huodong_unselected.png"),
                    //       width: 30.w,
                    //       height: 30,
                    //     ),
                    //     activeIcon: Image(
                    //       image: AssetImage(
                    //           "assets/home_img/tab_icon_huodong_selected.png"),
                    //       width: 30.w,
                    //       height: 30,
                    //     )),
                    BottomNavigationBarItem(
                        label: l10n.homeBottomNavigationBarItemLabel,
                        icon: Image(
                          image: const AssetImage(
                              "assets/home_img/tab_icon_kaoshi_unselected.png"),
                          width: 24.w,
                          height: 24.h,
                        ),
                        activeIcon: Image(
                          image: const AssetImage(
                              "assets/home_img/tab_icon_kaoshi_selected.png"),
                          width: 24.w,
                          height: 24.h,
                        )),
                    BottomNavigationBarItem(
                      label: l10n.findBottomNavigationBarItemLabel,
                      icon: Stack(
                        alignment: Alignment.topRight,
                        children: [
                          Image(
                            image: const AssetImage(
                                "assets/home_img/tab_icon_faxian_unselected.png"),
                            width: 24.w,
                            height: 24.h,
                          ),
                          logic.isShowRedPointInFind
                              ? Container(
                                  width: 6.w,
                                  height: 6.w,
                                  decoration: BoxDecoration(
                                    color: Colors.red,
                                    borderRadius: BorderRadius.circular(3.w),
                                  ),
                                )
                              : const SizedBox()
                        ],
                      ),
                      activeIcon: Image(
                        image: const AssetImage(
                            "assets/home_img/tab_icon_faxian_unselected.png"),
                        width: 24.w,
                        height: 24.h,
                      ),
                    ),
                    BottomNavigationBarItem(
                        label: l10n.mineBottomNavigationBarItemLabel,
                        icon: Stack(
                          alignment: Alignment.topRight,
                          children: [
                            Image(
                              image: const AssetImage(
                                  "assets/home_img/tab_icon_my_unselected.png"),
                              width: 24.w,
                              height: 24.h,
                            ),
                            logic.isShowMineRedPoint
                                ? Container(
                              width: 6.w,
                              height: 6.w,
                              decoration: BoxDecoration(
                                color: Colors.red,
                                borderRadius: BorderRadius.circular(3.w),
                              ),
                            )
                                : const SizedBox()
                          ],
                        ),
                        activeIcon: Image(
                          image: const AssetImage(
                              "assets/home_img/tab_icon_my_selected.png"),
                          width: 24.w,
                          height: 24.h,
                        )),
                  ],
                ),
              ),
            );
          })),
    );
  }

  /**
   * 方法：计时悬浮窗
   */
  void _addFloatingEndTimingButton() async {
    TimingHelper.remoteTrainValueNotifier?.addListener(
      () {
        final value = TimingHelper.remoteTrainValueNotifier?.value ?? false;
        Logs.e('xxx app 111 value = ${value}');

        if (value) {
          // 过程打卡监听
          removeTimingProcessLogic();
          addTimingProcessLogic();
          // 结束计时浮标
          DragOverlay.show(
              context: Global.appContext(),
              view: JkInkWell(
                  onTap: () {
                    Logs.e(
                        'xxx app 111 点击了结束计时  value = ${value}, state = ${SignProcessDialog.state()}');
                    //显示结束计时弹窗-value 监听触发，showProcess 是否已弹出过程打卡弹窗
                    if (value && !SignProcessDialog.state()) {
                      ThrottleUtil.get().throttle(() {
                        TimingHelper.endTimingDialog(
                            Global.appContext(), 'normal', (p0) {});
                      })();
                    }
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Container(
                        color: Colors.transparent,
                        width: 83,
                        height: 75,
                        child: Image.asset('assets/timing/ic_stop_timing.png')),
                  )));
        } else {
          removeTimingProcessLogic();
          DragOverlay.remove();
        }
      },
    );
  }

  /**
   * 方法：添加过程打卡监听
   */
  void addTimingProcessLogic() {
    // 判断是否开启了过程拍照
    final openProcess = ITiming.get().openProcessPhoto ?? false;
    if (!openProcess) {
      return;
    }
    // 判断是否是理论培训
    final trainType = ITiming.get().TrainSubType;
    if (trainType != 1) {
      return;
    }
    // 判断是否是开启严格计时
    final isStrict = ITiming.get().openStrictTiming ?? false;
    if (!isStrict) {
      /// 监听过程拍照
      TimingHelper.monitorProcessVerify(() {
        // 旋转屏幕
        SystemChrome.setPreferredOrientations([
          DeviceOrientation.portraitUp,
          DeviceOrientation.portraitDown,
        ]);

        //显示过程打卡弹窗-isRemoteTrain 是否正在计时
        if (mounted && TimingHelper.isRemoteTrain) {
          TimingHelper.processTimingDialog(context, (p0) {});
        } else {
          removeTimingProcessLogic();
        }
      });
    }
  }

  /**
   * 方法：释放过程打卡监听
   */
  void removeTimingProcessLogic() {
    // 判断是否正在计时
    if (TimingHelper.isRemoteTrain) {
      return;
    }
    // 判断是否开启了过程拍照
    final openProcess = ITiming.get().openProcessPhoto ?? false;
    if (!openProcess) {
      return;
    }
    // 判断是否是理论培训
    final trainType = ITiming.get().TrainSubType;
    if (trainType != 1) {
      return;
    }
    // 判断是否是开启严格计时
    final isStrict = ITiming.get().openStrictTiming ?? false;
    if (!isStrict) {
      /// 取消监听过程拍照
      TimingHelper.cancelMonitorProcessVerify();
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      // 闲置状态 比如接打电话等，处于这种状态的应用程序应该假设它们可能在任何时候暂停。
      case AppLifecycleState.inactive:
        // logD('APP 处于非活动状态');
        // if(_overlayEntry!=null) {
        //   _overlayEntry?.remove();
        //   // _overlayEntry = null;
        //   Overlay.of(context).insert(_overlayEntry!);
        // }
        // Overlay.of(context).insert(_overlayEntry!);
        break;
      case AppLifecycleState.resumed: // 应用程序可见，前台
        // if(!BeiziAdPlugin.instance.advertIsBanPlay&&!BeiziAdPlugin.instance.isBanCoolStartProcess){
        //   if(!overlayEntry!.mounted){
        //     Overlay.of(context).insert(overlayEntry!);
        //   }
        // }
        int? currentTime = await PreferencesService().getInt('exitAppTime');
        if (isTimeDifferenceExceeded(
          currentTime ?? 0,
          BeiziAdPlugin.instance.minutes,
        )) {
          ListData? list = BeiziAdPlugin.instance.getSplashData();
          await BeiziAdPlugin.instance
              .getSplashAdMethod(list, isFirstSlash: false);
          BuryingPointUtils.instance.addPoint(
              buryingPointList: BuryingPointList(
                  eventType: 7, entranceType: 1, action: 2, browseDuration: 0));
        } else {
          // Future.delayed(Duration(milliseconds: 500,),(){
          //   hideOverlay();
          // });
        }
        BeiziAdPlugin.instance.advertIsBanPlay = false;
        await PreferencesService().removeKey('exitAppTime');
        onResumed();
        Logs.e('xxx app 进入前台');
        break;
      case AppLifecycleState.paused: // 应用程序不可见，后台
        // BeiziAdPlugin.instance.isBanCoolStartProcess=false;
        saveCurrentTimestamp();
        Logs.e('xxx app ${ITiming.get().toMiniProgram}');
        if (!ITiming.get().toMiniProgram) {
          onPaused();
          Logs.e('xxx app 进入后台');
        }
        break;
      case AppLifecycleState.detached:
        // logD('app 托管中');
        break;
      case AppLifecycleState.hidden:
        break;
    }
  }

  // static void hideOverlay() {
  //   try {
  //     if (overlayEntry != null) {
  //       if(overlayEntry!.mounted){
  //         overlayEntry?.remove();
  //       }
  //       // _overlayEntry = null;
  //     }
  //   } catch (e) {
  //     print("eee");
  //   }
  // }

  // void showOverlay(BuildContext context) {
  //   final overlay = Overlay.of(context);
  //   if (overlay != null && overlayEntry == null) {
  //     overlayEntry = OverlayEntry(
  //       builder: (context) => Positioned(
  //           // right: 16.0,
  //           // bottom: 16.0,
  //           child: assImg(
  //               img: 'img_qidong_zuoti',
  //               fit: BoxFit.fill,
  //               w: MediaQuery.of(context).size.width,
  //               h: double.infinity)),
  //     );
  //     overlay.insert(overlayEntry!);
  //   }
  // }

  void saveCurrentTimestamp() {
    PreferencesService()
        .setInt('exitAppTime', DateTime.now().millisecondsSinceEpoch);
  }

  bool isTimeDifferenceExceeded(int targetTimestamp, int minutes) {
    if (targetTimestamp == 0) {
      return false;
    }
    // 获取当前时间戳
    int currentTimestamp = DateTime.now().millisecondsSinceEpoch;

    // 计算时间差（毫秒）
    int difference = currentTimestamp - targetTimestamp;

    // 将分钟转换为毫秒（1分钟 = 60,000毫秒）
    int minutesInMilliseconds = minutes * 60 * 1000;

    // 判断时间差是否大于指定的分钟数
    return difference > minutesInMilliseconds;
  }

  /**
   * 方法：app回到前台
   */
  void onResumed() {
    if (!TimingHelper.isSupplEnd) {
      if (mounted) {
        // 严格从业计时 也使用此方法
        // 普通计时状态，恢复计时逻辑
        TimingHelper.resumeRemoteTrain(context);
      }
    }
    Logs.e("@@@@@@@ 设置状态栏 status ${UIUtils.status}");
    if (Platform.isAndroid) {
      UIUtils.statusBarLight();
    }
  }

  /**
   * 方法：app切换后台
   */
  void onPaused() {
    final openStrictTiming = ITiming.get().openStrictTiming;
    if (mounted && openStrictTiming == null ||
        (openStrictTiming != null && !openStrictTiming)) {
      // 普通计时状态，暂停计时逻辑
      TimingHelper.pauseRemoteTrain();
    }
  }

// class TabContainerScreen extends StatelessWidget {
//   final VoidCallback gotoLogin;
//   final UserRepository userRepository;
//   var isToken;
//
//   TabContainerScreen({Key? key,required this.userRepository,required this.gotoLogin}) : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     final l10n = AppLocalizations.of(context);
//     final tabState = CupertinoTabPage.of(context);
//     final theme = DxjdTheme.of(context);
//     debugPrint('getUs--${getUs().then((value) => debugPrint(value))}==----${userRepository.isAutoLogin()}-------${getUs()==''}');
//     return StatefulWrapper(
//       onInit: () {
//         // isToken=await getUs();
//         // getUs().then((value) {
//         //   isToken=value;
//         // });
//         },
//       child: Stack(
//       children: <Widget>[
//         CupertinoTabScaffold(
//           controller: tabState.controller,
//           tabBuilder: tabState.tabBuilder,
//           tabBar: CupertinoTabBar(
//             activeColor: theme.mainColor,
//             inactiveColor: theme.weakTextColor,
//             items: [
//               BottomNavigationBarItem(
//                   label: l10n.homeBottomNavigationBarItemLabel,
//                   icon: Image(image: AssetImage("assets/home_img/tab_icon_kaoshi_unselected.png"),width: 30.w,height: 30,),
//                   activeIcon: Image(image: AssetImage("assets/home_img/tab_icon_kaoshi_selected.png"),width: 30.w,height: 30,)
//               ),
//               BottomNavigationBarItem(
//                 label: l10n.findBottomNavigationBarItemLabel,
//                 icon: Image(image: AssetImage("assets/home_img/tab_icon_faxian_unselected.png"),width: 30.w,height: 30,),
//                 activeIcon: Image(image: AssetImage("assets/home_img/tab_icon_faxian_unselected.png"),width: 30.w,height: 30,),
//               ),
//               BottomNavigationBarItem(
//                   label: l10n.mineBottomNavigationBarItemLabel,
//                   icon: Image(image: AssetImage("assets/home_img/tab_icon_my_unselected.png"),width: 30.w,height: 30,),
//                   activeIcon: Image(image: AssetImage("assets/home_img/tab_icon_my_selected.png"),width: 30.w,height: 30,)
//               ),
//             ],
//           ),
//         ),
//         if (getUs()==null) // 如果用户未登录
//           Positioned.fill(
//             child: GestureDetector(
//               onTap: () {
//                 gotoLogin.call();
//                 // 在这里处理点击事件，例如打开登录界面
//               },
//               child: Container(
//                 color: Colors.transparent,
//               ),
//             ),
//           ),
//       ],
//     ),
//     );
//
//
//   }
//   Future getUs() async {
//     final T=await userRepository.getUserToken();
//     debugPrint('T:-----$T');
//     return T;
//   }
// }
}

class StatefulWrapper extends StatefulWidget {
  final Function onInit;
  final Widget child;

  const StatefulWrapper({required this.onInit, required this.child});

  @override
  _StatefulWrapperState createState() => _StatefulWrapperState();
}

class _StatefulWrapperState extends State<StatefulWrapper> {
  @override
  void initState() {
    if (widget.onInit != null) {
      widget.onInit();
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
