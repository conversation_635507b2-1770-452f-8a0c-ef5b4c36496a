import 'dart:convert';
import 'dart:io';
import 'package:api/src/models/response/home/<USER>/bring_view_examination_detail_model.dart';
import 'package:api/src/models/response/home/<USER>/examination_room_route_rm.dart';
import 'package:api/src/models/response/home/<USER>/home_banner_rm.dart';
import 'package:api/src/models/response/home/<USER>/bring_view_examination_model.dart';
import 'package:api/src/models/response/home/<USER>/theory_video_menu_rm.dart';
import 'package:api/src/models/response/user_account_rm.dart';
import 'package:api/src/request/base_request.dart';
import 'package:api/src/request/face/face.dart';
import 'package:api/src/request/home/<USER>';
import 'package:api/src/request/home/<USER>/get_spring_view_examination_detail_request.dart';
import 'package:api/src/request/home/<USER>/get_spring_view_examination_request.dart';
import 'package:api/src/request/home/<USER>';
import 'package:api/src/request/home/<USER>';
import 'package:api/src/request/home/<USER>';
import 'package:api/src/request/home/<USER>';
import 'package:api/src/request/home/<USER>';
import 'package:api/src/request/home/<USER>/get_sub_video_detail_request.dart';
import 'package:api/src/request/home/<USER>';
import 'package:api/src/request/home/<USER>';
import 'package:api/src/request/home/<USER>/get_dialog_config.dart';
import 'package:api/src/request/home/<USER>/get_point_map_vip_request.dart';
import 'package:api/src/request/home/<USER>/get_sub_point_map_request.dart';
import 'package:api/src/request/home/<USER>/get_time_table_request.dart';
import 'package:api/src/request/home/<USER>/home_article_info_request.dart';
import 'package:api/src/request/home/<USER>/home_banner_no_token_request.dart';
import 'package:api/src/request/home/<USER>/home_banner_request.dart';
import 'package:api/src/request/home/<USER>/open_ponint_vip_request.dart';
import 'package:api/src/request/home/<USER>';
import 'package:api/src/request/home/<USER>';
import 'package:api/src/request/home/<USER>';
import 'package:api/src/request/home/<USER>';
import 'package:api/src/request/home/<USER>';
import 'package:api/src/request/home/<USER>';
import 'package:api/src/request/home/<USER>';
import 'package:api/src/request/home/<USER>';
import 'package:api/src/request/home/<USER>/setVideoProgress.dart';
import 'package:api/src/request/home/<USER>/update_video_num.dart';
import 'package:api/src/request/home/<USER>/get_hot_live_four_request.dart';
import 'package:api/src/request/home/<USER>/get_hot_live_request.dart';
import 'package:api/src/request/home/<USER>/get_practitioner_list.dart';
import 'package:api/src/request/shop/ali_pay_request.dart';
import 'package:api/src/request/shop/get_apple_pay_id.dart';
import 'package:api/src/request/shop/shop_pay_token_request.dart';
import 'package:api/src/request/timing/timing_drop_request.dart';
import 'package:api/src/request/timing/timing_isrecord_request.dart';
import 'package:api/src/request/timing/timing_list_request.dart';
import 'package:api/src/request/timing/timing_query_unfinished_request.dart';
import 'package:api/src/request/timing/timing_student_verify_manual_request.dart';
import 'package:api/src/request/timing/timing_student_verify_request.dart';
import 'package:api/src/request/user/bind_phone_with_one_key.dart';
import 'package:api/src/request/user/get_coupon_rquest.dart';
import 'package:api/src/request/user/login_get_verify_code.dart';
import 'package:api/src/request/user/query_user_open_info_request.dart';
import 'package:api/src/request/user/receive_coupon_request.dart';
import 'package:api/src/request/user/refresh_studnt_doc_request.dart';
import 'package:api/src/request/user/study_prove_status_request.dart';
import 'package:api/src/request/user/update_user_avtar.dart';
import 'package:api/src/request/user/user_vip_product_request.dart';
import 'package:api/src/request/user/view_pdf_contract.dart';
import 'package:api/src/request/user/vx_bind_request.dart';
import 'package:flutter/cupertino.dart';
import 'package:tools/tools.dart';
import 'http/http_constant.dart';
import 'http/http_manager.dart';
import 'package:api/src/request/request.dart';
import 'package:api/src/models/models.dart';

import 'models/response/home/<USER>/home_article_info_rm.dart';
import 'models/response/home/<USER>/theory_video_list_rm.dart';
import 'request/home/<USER>';
import 'request/home/<USER>';
import 'request/home/<USER>/examination_room_route_request.dart';
import 'request/home/<USER>/examination_room_vip_video_id_request.dart';
import 'request/home/<USER>';
import 'request/home/<USER>';
import 'request/home/<USER>/get_practical_video_request.dart';
import 'request/home/<USER>/get_theory_video_detail_request.dart';
import 'request/home/<USER>/get_theory_video_list_request.dart';
import 'request/home/<USER>/get_theory_video_menu_db_request.dart';
import 'request/home/<USER>/get_theory_video_menu_dt_request.dart';
import 'request/home/<USER>/get_theory_video_menu_request.dart';
import 'request/home/<USER>/get_theory_video_progress_list_request.dart';
import 'request/home/<USER>/get_theory_video_progress_request.dart';
import 'request/shop/query_apple_pay_status.dart';
import 'request/shop/upload_apple_voucher_request.dart';
import 'request/timing/timing_upload_photo_liaoning_request.dart';
import 'request/user/bind_by_wechat_request.dart';
import 'request/user/bind_phone_with_mobile_verify.dart';
import 'request/user/bind_student_doc_request.dart';
import 'request/user/login_by_apple_request.dart';
import 'request/user/login_by_phone_onekey_request.dart';
import 'request/user/login_by_phone_request.dart';
import 'request/user/login_by_wechat_request.dart';
import 'request/user/logoff_request.dart';
import 'request/user/modify_pass_word_request.dart';
import 'request/user/reset_pass_word_request.dart';
import 'request/user/send_video_question_notice_request.dart';
import 'request/user/student_query_doc.dart';
import 'request/user/update_student_train_type_request.dart';

typedef UserTokenSupplier = Future<String?> Function();

const _ossTokenEnvironmentVariableKey = 'ovsx-oss-token';

class Api {
  static const _errorCodeJsonKey = 'error_code';
  static const _errorMessageJsonKey = 'message';
  static bool testToken = false;

  Api({
    required UserTokenSupplier userTokenSupplier,
    HttpManager? http,
  }) : _http = http ?? HttpManager.getInstance() {
    _http.userTokenSupplier = userTokenSupplier;
  }

  final HttpManager _http;

  Map<String, dynamic>? _ossTokenData;
}

extension LoginExtension on Api {
  /// 用户
  // 1，检测用户是否已存在
  Future<bool> userIsExist({required String userName}) async {
    try {
      final request = CheckUserExistsRequest();
      request.add('Id', userName);
      final response = await _http.fire(request);
      final isExists = response['exists'] as bool;
      return isExists;
    } catch (_) {
      rethrow;
    }
  }
}

extension LoginPassWord on Api {
  Future<String?> loginPassWord(
      {required String userName,
      required String passWord,
      required String key}) async {
    try {
      final request = LoginByUserRequest();
      if (Platform.isAndroid) {
        request.header['appTerminal'] =
            (await Tools.getDeviceInfo()).toUpperCase();
      } else {
        request.header['appTerminal'] = "APPLE";
      }
      request.header['appVersion'] = await Tools.getAppVersion();
      request.header['appModel'] = await Tools.getPhoneModel();
      request.add('Flag', userName);
      request.add('Password', passWord);
      request.add('Client', HttpConstant.Client);
      request.add('Key', key);
      final response = await _http.fire(request, showLoading: true);
      final token = response['token'];
      return token;
    } catch (_) {
      rethrow;
    }
  }
}

extension LoginGetPassWordKey on Api {
  Future<Map> getPassWordKey() async {
    try {
      final request = ApplyPasswordKeyRequest();
      request.add('client', HttpConstant.Client);
      final response = await _http.fire(request);
      final token = response['token'];
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

extension Loginphone on Api {
  Future<Map> getPhoneLogin(phone, ver) async {
    try {
      final request = LoginByPhoneUserRequest();
      if (Platform.isAndroid) {
        request.header['appTerminal'] =
            (await Tools.getDeviceInfo()).toUpperCase();
      } else {
        request.header['appTerminal'] = "APPLE";
      }
      request.header['appVersion'] = await Tools.getAppVersion();
      request.header['appModel'] = await Tools.getPhoneModel();
      request.add('Client', HttpConstant.Client);
      request.add('Mobile', phone);
      request.add('VerifyCode', ver);
      final response = await _http.fire(request, showLoading: true);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

extension LoginWeChat on Api {
  Future<Map> getWeChatLogin(String code) async {
    try {
      final request = LoginByWeChatUserRequest();
      if (Platform.isAndroid) {
        request.header['appTerminal'] =
            (await Tools.getDeviceInfo()).toUpperCase();
      } else {
        request.header['appTerminal'] = "APPLE";
      }
      request.header['appVersion'] = await Tools.getAppVersion();
      request.header['appModel'] = await Tools.getPhoneModel();
      request.add('Client', HttpConstant.Client);
      request.add('Code', code);
      final response = await _http.fire(request, showLoading: true);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

//绑定微信
extension BindWeChat on Api {
  Future<Map> bindWeChat(String code) async {
    try {
      final request = BindByWeChatUserRequest();
      request.add('Code', code);
      request.add('Client', HttpConstant.Client);
      final response = await _http.fire(request, showLoading: true);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

extension LoginGetVer on Api {
  Future<Map> getPhoneVerLogin(phone) async {
    try {
      final request = LoginGetVerRequest();
      request.add('Client', HttpConstant.Client);
      request.add('Mobile', phone);
      final response = await _http.fire(request, showLoading: true);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

extension QueryUserInfo on Api {
  Future<UserAccountRm> queryUserInfo(
      {bool showLoading = true, bool isNeedUpdateInfo = true}) async {
    try {
      final request = UserInfoRequest();
      final response = await _http.fire(request, showLoading: showLoading);
      ITools.get().setUserAccount(response, isNeedUpdateInfo: isNeedUpdateInfo);
      return UserAccountRm.fromJson(response);
    } catch (_) {
      rethrow;
    }
  }
}

extension QueryStudentDoc on Api {
  Future<Map> queryStudentDoc(String id) async {
    try {
      final request = StudentDocRequest();
      request.add('Id', id);
      final response = await _http.fire(request, showLoading: true);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

extension BindStudentDoc on Api {
  Future<Map> bindStudentDoc(String id) async {
    try {
      final request = BindStudentDocRequest();
      request.add('Id', id);
      final response = await _http.fire(request, showLoading: true);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

extension LoginOut on Api {
  Future<Map> loginOut(String userFlag) async {
    try {
      final request = LogoutRequest();
      request.add('Client', HttpConstant.Client);
      request.add('UserFlag', userFlag);
      final response = await _http.fire(request, showLoading: true);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

extension UpdateStudentTrainType on Api {
  Future<Map> updateStudentTrainType(String topicType, int division) async {
    try {
      final request = UpdateStudentTrainTypeRequest();
      request.add('TopicType', topicType);
      request.add('Division', division);
      final response = await _http.fire(request, showLoading: true);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

extension UpdateUserAvatar on Api {
  Future<Map> updateUserAvatar(
    String url,
  ) async {
    try {
      final request = UpdateUserAvtarRequest();
      request.add('Id', url);
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

extension LoginPhoneWithOneKey on Api {
  Future<Map> loginPhoneWithOneKey(String accessToken) async {
    try {
      final request = LoginPhoneWithOneKeyRequest();
      request.add('Client', HttpConstant.Client);
      request.add('AccessToken', accessToken);
      final response = await _http.fire(request, showLoading: true);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

extension VXPhoneWithOneKey on Api {
  Future<Map> vXPhoneWithOneKey({
    String? mobile,
    String? code,
    required String accessToken,
    required String openId,
    required int isSimple,
    String? aliAccessToken,
  }) async {
    try {
      final request = VXBindRequest();
      if (Platform.isAndroid) {
        request.header['appTerminal'] =
            (await Tools.getDeviceInfo()).toUpperCase();
      } else {
        request.header['appTerminal'] = "APPLE";
      }
      request.header['appVersion'] = await Tools.getAppVersion();
      request.header['appModel'] = await Tools.getPhoneModel();
      if (isSimple == 2) {
        request.add('Mobile', mobile!);
        request.add('Code', code!);
      } else {
        request.add('AliAccessToken', aliAccessToken!);
      }
      request.add('Client', HttpConstant.Client);
      request.add('BindType', 'WX');
      request.add('OpenId', openId);
      request.add('IsSimple', isSimple);
      request.add('AccessToken', accessToken);
      final response = await _http.fire(request, showLoading: true);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

extension LoginWithApple on Api {
  Future<Map> loginWithAppleId({
    required String name,
    required String email,
    required String userIdentifier,
    required String identityToken,
    required String authorizationCode,
  }) async {
    try {
      final request = LoginWithAppleIdRequest();
      if (Platform.isAndroid) {
        request.header['appTerminal'] =
            (await Tools.getDeviceInfo()).toUpperCase();
      } else {
        request.header['appTerminal'] = "APPLE";
      }
      request.header['appVersion'] = await Tools.getAppVersion();
      request.header['appModel'] = await Tools.getPhoneModel();
      request.add('Client', HttpConstant.Client);
      request.add('Name', name);
      request.add('Email', email);
      request.add('UserIdentifier', userIdentifier);
      request.add('IdentityToken', identityToken);
      request.add('AuthorizationCode', authorizationCode);
      final response = await _http.fire(request, showLoading: true);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

extension BindPhoneWithOneKey on Api {
  Future<Map> bindPhoneWithOneKey(String accessToken) async {
    try {
      final request = BindPhoneWithOneKeyRequest();
      request.add('AccessToken', accessToken);
      final response = await _http.fire(request, showLoading: true);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

extension RefreshToken on Api {
  Future<Map> refreshToken(String userFlag) async {
    try {
      final request = RenewalUserTokenRequest();
      request.add('Client', HttpConstant.Client);
      request.add('UserFlag', userFlag);
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

extension BindPhoneWithMobileVer on Api {
  Future<Map> bindPhoneWithMobileVerify(String mobile, String code) async {
    try {
      final request = BindPhoneWithMobileVerifyRequest();
      request.add('Mobile', mobile);
      request.add('Code', code);
      final response = await _http.fire(request, showLoading: true);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

extension QueryUserAccountOpenInfo on Api {
  Future<Map> queryUserAccountOpenInfo() async {
    try {
      final request = QueryUserAccountOpenInfoRequest();
      final response = await _http.fire(request, showLoading: true);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

extension Logoff on Api {
  Future<Map> logoff(String mobile, String code) async {
    try {
      final request = LogoffRequest();
      request.add('Mobile', mobile);
      request.add('Code', code);
      final response = await _http.fire(request, showLoading: true);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

extension ModifyUserPassword on Api {
  Future<Map> modifyUserPassword(
      String key, String oldPassword, String newPassword) async {
    try {
      final request = ModifyUserPasswordRequest();
      request.add('Key', key);
      request.add('PasswordOld', oldPassword);
      request.add('Password', newPassword);
      final response = await _http.fire(request, showLoading: true);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

extension ResetPassword on Api {
  Future<Map> resetPassword(key,
      {required String phone,
      required String ver,
      required String account,
      required passWord}) async {
    try {
      final request = ResetPasswordRequest();
      request.add('Key', key);
      request.add('Mobile', phone);
      request.add('UserName', account);
      request.add('Password', passWord);
      request.add('VerifyCode', ver);
      final response = await _http.fire(request, showLoading: true);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

///==========================================首页相关===========================================///

extension GetTheoryVideoMenu on Api {
  Future<List<TheoryVideoMenuRm?>> getTheoryVideoMenu(
      {required int subject}) async {
    try {
      final request = GetTheoryVideoMenuRequest();
      request.add('Subject', subject);
      final response = await _http.fire(request, showLoading: false);
      final List<TheoryVideoMenuRm?> result = [];
      for (final item in response) {
        result.add(TheoryVideoMenuRm.fromJson(item));
      }
      return result;
    } catch (_) {
      rethrow;
    }
  }
}

///客车
extension GetTheoryVideoMenuDT on Api {
  Future<List<TheoryVideoMenuRm?>> getTheoryVideoMenuDT(
      {required int subject}) async {
    try {
      final request = GetTheoryVideoMenuDTRequest();
      request.add('Subject', subject);
      final response = await _http.fire(request, showLoading: false);
      final List<TheoryVideoMenuRm?> result = [];
      for (final item in response) {
        result.add(TheoryVideoMenuRm.fromJson(item));
      }
      return result;
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

///货车
extension GetTheoryVideoMenuDB on Api {
  Future<List<TheoryVideoMenuRm?>> getTheoryVideoMenuDB(
      {required int subject}) async {
    try {
      final request = GetTheoryVideoMenuDBRequest();
      request.add('Subject', subject);
      final response = await _http.fire(request, showLoading: false);
      final List<TheoryVideoMenuRm?> result = [];
      for (final item in response) {
        result.add(TheoryVideoMenuRm.fromJson(item));
      }
      return result;
    } catch (_) {
      rethrow;
    }
  }
}

///从业培训
extension GetPractitionerDB on Api {
  Future<List<TheoryVideoMenuRm?>> getPractitionerDB(
      {required int subject}) async {
    try {
      final request = GetPractitionerListRequest();
      request.add('Subject', subject);
      final response = await _http.fire(request, showLoading: false);
      final List<TheoryVideoMenuRm?> result = [];
      for (final item in response) {
        result.add(TheoryVideoMenuRm.fromJson(item));
      }
      return result;
    } catch (_) {
      rethrow;
    }
  }
}

///获取未读消息
extension GetUnReadMessage on Api {
  Future getUnReadMessage() async {
    try {
      final request = getUnReadMessageRequest();
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

extension GetTheoryVideoList on Api {
  Future<TheoryVideoListRm?> getTheoryVideoList(
      {index, rows, required List<String> menuList}) async {
    try {
      final request = GetTheoryVideoListRequest();
      request.add('Index', index);
      request.add('Rows', rows);
      request.add('Menus', menuList);
      final response = await _http.fire(request, showLoading: false);
      return TheoryVideoListRm.fromJson(response);
    } catch (_) {
      rethrow;
    }
  }
}

extension GetVipHelpNum on Api {
  Future getVipHelpNum() async {
    try {
      final request = GetVipHelpNumRequest();
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

///获取拿证后的商品服务列表
extension GetTheCertificateData on Api {
  Future getTheCertificateData(
      {List<String>? category, List<String>? categorySub}) async {
    try {
      final request = GetTheCertificateDataRequest();
      request.add('Index', 1);
      request.add('Rows', 20);
      if (category != null) {
        request.add('Category', category);
      }
      if (categorySub != null) {
        request.add('CategorySub', categorySub);
      }
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }

  ///获取拿证后的商品服务开通状态
  Future getTheCertificateStatusData(String id) async {
    try {
      final request = GetTheCertificateStatusDataRequest();
      request.add('Id', id);
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }

  ///根据产品id查询销售商品id
  Future getTheCertificateStatusDataId(String id) async {
    try {
      final request = GetTheCertificateStatusDataIdRequest();
      request.add('Id', id);
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

///获取课程
extension GetCertifications on Api {
  Future getCertificateDetailData(String id) async {
    try {
      final request = GetCertificateRequest();
      request.add('Id', id);
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }

  Future getCertificateDetailVideoList(String id) async {
    try {
      final request = GetCertificateDetailVideoListRequest();
      request.add('Collection', [id]);
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }

  //  获取资料实战数据
  Future getCertificateDetailDataList(String id) async {
    try {
      final request = GetCertificateDetailDataListRequest();
      request.add('Id', id);
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

//获取广告配置
extension GetAdConfig on Api {
  Future getAdvertisingConfig() async {
    try {
      final request = GetAdConfigRequest();
      request.add('PlatformCode', Platform.isAndroid ? 2 : 1);
      request.add('PlatformType', "bz");
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }

  Future getSlashAdvertisingConfig() async {
    try {
      final request = GetSplashAdConfigRequest();
      request.add('PlatformCode', Platform.isAndroid ? 2 : 1);
      request.add('PlatformType', "bz");
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }

  Future countADNum() async {
    try {
      final request = CountAdNumRequest();
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

//点位图
extension GetPointMap on Api {
  Future getSubjectThreeExaminationPointVipMap(int id) async {
    try {
      final request = GetPointMapVipRequest();
      request.add('Id', id);
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }

  Future openPointVip(String productId) async {
    try {
      final request = GetOpenPointVipInfoRequest();
      request.add('Id', productId);
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }

  Future getPointMap(int division) async {
    try {
      final request = GetPointMapRequest();
      request.add('City', division);
      request.add('Index', 1);
      request.add('Rows', 200);
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

extension GetVideoDetail on Api {
  Future getVideoDetail({required List<String> vidList}) async {
    try {
      final request = GetSubVideoDetailRequest();
      request.add('Collection', vidList);
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

extension GetScoreList on Api {
  Future getScoreList() async {
    try {
      final request = GetScoreListRequest();
      request.add('Index', 1);
      request.add('Rows', 100);
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

extension GetHotLiveList on Api {
  Future getHotLiveList(bool isSubjectOne) async {
    try {
      final request;
      if (isSubjectOne) {
        request = GetHotLiveRequest();
      } else {
        request = GetHotLiveFourRequest();
      }
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

extension IsWriteQuestionnaire on Api {
  Future<bool> isWriteQuestionnaire() async {
    try {
      final request = CheckIsFirstWriteRequest();
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

extension GetTheoryVideoDetail on Api {
  Future<Map> getTheoryVideoDetail({vid}) async {
    try {
      final request = GetTheoryVideoDetailRequest();
      request.add('Id', vid);
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

extension GetVideoProgress on Api {
  Future<Map> getVideoProgress({id}) async {
    try {
      final request = GetVideoProgressRequest();
      request.add('Id', id);
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

///批量获取视频进度
extension GetVideoProgressList on Api {
  Future<List> getVideoProgressList({idList}) async {
    try {
      final request = GetVideoProgressListRequest();
      request.add('Collection', idList);
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }

  ///设置视频进度
  Future<Map> setVideoProgress({required vid, required progress}) async {
    try {
      final request = SetVideoProgress();
      request.add('VideoId', vid);
      request.add('Progress', progress);
      final response = await _http.fire(request, showLoading: false);
      return response;
      // return ExaminationRoomRouteListRm.fromJson(response) ;
    } catch (_) {
      rethrow;
    }
  }

  ///更新视频次数
  Future<Map> updateVideoNum({required vid}) async {
    try {
      final request = UpdateVideoNum();
      request.add('Id', vid);
      final response = await _http.fire(request, showLoading: true);
      return response;
      // return ExaminationRoomRouteListRm.fromJson(response) ;
    } catch (_) {
      rethrow;
    }
  }
}

//科二实操视频

extension GetSubjectTwoPracticalVideo on Api {
  Future<TheoryVideoListRm?> getSubjectTwoPracticalVideo(
      {required int subject}) async {
    try {
      final request = GetSubjectTwoPracticalVideoRequest();
      request.add('Index', 1);
      request.add('Rows', 20);
      request.add('Subject', subject);
      final response = await _http.fire(request, showLoading: false);
      return TheoryVideoListRm.fromJson(response);
    } catch (_) {
      rethrow;
    }
  }
}

///-----科二考场路线-----///
extension GetExaminationRoomRoute on Api {
  Future<ExaminationRoomRouteListRm> getExaminationRoomRoute(
      {required int subject,
      required int index,
      required int rows,
      required int simulation,
      int? province}) async {
    try {
      final request = GetExaminationRoomRouteRequest();
      request.add('Index', index);
      request.add('Rows', rows);
      request.add('Subject', subject);
      if (subject == 3) {
        request.add('Simulation', simulation);
      }
      // request.add('Province', province!);
      request.add('City', province!);
      final response = await _http.fire(request, showLoading: false);
      return ExaminationRoomRouteListRm.fromJson(response);
    } catch (_) {
      rethrow;
    }
  }
}

///-----带你看考场数据-----///
extension GetBringViewExamination on Api {
  Future<BringVIewExaminationModel> getBringViewExamination({
    required int index,
    required int rows,
    required int city,
  }) async {
    try {
      final request = GetSpringViewExaminationRequest();
      request.add('Index', index);
      request.add('Rows', rows);
      request.add('City', city);
      final response = await _http.fire(request, showLoading: false);
      return BringVIewExaminationModel.fromJson(response);
    } catch (_) {
      rethrow;
    }
  }
}

///-----带你看考场数据-----///
extension GetBringViewExaminationDetail on Api {
  Future<BringVIewExaminationDetailModel> getBringViewExaminationDetail({
    required String id,
  }) async {
    try {
      final request = GetSpringViewExaminationDetailRequest();
      request.add('Id', id);
      final response = await _http.fire(request, showLoading: false);
      return BringVIewExaminationDetailModel.fromJson(response);
    } catch (_) {
      rethrow;
    }
  }
}

///-----获取科二VIP视频详情ID-----///
extension GetSubTwoVipVideoId on Api {
  Future<Map> getSubTwoVipVideoId({required vid}) async {
    try {
      final request = GetSubTwoVipVideoIdRequest();
      request.add('Id', vid);
      final response = await _http.fire(request, showLoading: true);
      return response;
      // return ExaminationRoomRouteListRm.fromJson(response) ;
    } catch (_) {
      rethrow;
    }
  }
}

///-----获取科一学时历程信息-----///
extension GetSubjectOneTimeTable on Api {
  Future<Map> getSubjectOneTimeTable({int? subject}) async {
    try {
      final request = GetSubjectOneTimeTableRequest();
      request.add('Subject', subject!);
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (e) {
      rethrow;
    }
  }

  //2.13.2.查询学员学时列表
  Future<Map> timingList({int? subject, int? index, int? rows}) async {
    try {
      final request = TimeListRequest();
      request.add('Subject', subject!);
      request.add('Index', index!);
      request.add('Rows', rows!);
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (e) {
      rethrow;
    }
  }
}

//首页Banner
extension GetHomeBanner on Api {
  Future<List<BannerDataRm>> getHomeBanner({
    int? district,
    List<int>? vipSubject,
    List<int>? subject,
  }) async {
    try {
      final request = GetHomeBannerRequest();
      request.add('Platform', Platform.isAndroid ? 'ANDROID' : 'IOS');
      request.add('District', district ?? 0);
      if (vipSubject != null) {
        request.add('VipSubject', vipSubject);
      }
      if (subject != null) {
        request.add('Subject', subject);
      }

      final response = await _http.fire(request, showLoading: false);
      List<BannerDataRm> result = [];
      for (final item in response) {
        result.add(BannerDataRm.fromJson(item));
      }
      // debugPrint("GetHomeBanner-----:"+result.toString());
      return result;
    } catch (e) {
      rethrow;
    }
  }

  Future<List<BannerDataRm>> getHomeNoTokenBanner({
    int? district,
    List<int>? vipSubject,
    List<int>? subject,
  }) async {
    try {
      final request = GetHomeBannerNoTokenRequest();
      request.add('Platform', Platform.isAndroid ? 'ANDROID' : 'IOS');
      request.add('District', district ?? 0);

      final response = await _http.fire(request, showLoading: false);
      List<BannerDataRm> result = [];
      for (final item in response) {
        result.add(BannerDataRm.fromJson(item));
      }
      // debugPrint("GetHomeBanner-----:"+result.toString());
      return result;
    } catch (e) {
      rethrow;
    }
  }

  Future getOpenVipProduct() async {
    try {
      final request = GetVipOpenStatusRequest();
      final response = await _http.fire(request, showLoading: false);
      // debugPrint("GetHomeBanner-----:"+result.toString());
      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future getDialogConfig({required List<int> type}) async {
    try {
      final request = GetDialogConfigQuest();
      request.add('Platform', Platform.isAndroid ? 'ANDROID' : 'IOS');
      request.add('Type', type);
      final response = await _http.fire(request, showLoading: false);
      // debugPrint("GetHomeBanner-----:"+result.toString());
      return response;
    } catch (e) {
      rethrow;
    }
  }
}

extension ViewPdfContract on Api {
  Future<Map> viewPdfContract() async {
    try {
      final request = GetViewPdfContractRequest();
      final response = await _http.fire(request, showLoading: true);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

extension CheckAppVersion on Api {
  Future<Map<String, dynamic>> checkAppVersion(
      {required String appVersion, required String channel}) async {
    try {
      final request = CheckAppVersionRequest();
      request.add('AppVersion', appVersion);
      request.add('AppTerminal', channel);
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

///==========首页学车资讯=============////////

extension GetArticleInfo on Api {
  Future<ArticleInfoDataRm> getArticleInfo({int? index, int? rows}) async {
    try {
      final request = GetArticleInfoRequest();
      request.add('Index', index ?? 1);
      request.add('Rows', rows ?? 10);
      request.add('Category', ['xw-article-dxjd-zx']);
      final response = await _http.fire(request, showLoading: false);
      return ArticleInfoDataRm.fromJson(response);
    } catch (_) {
      rethrow;
    }
  }
}

extension GetMyAchievementData on Api {
  Future getMyAchievementData() async {
    try {
      final request = GetMyAchievementDataRequest();
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

extension GetServerTime on Api {
  Future<int> getServerTime() async {
    try {
      final request = GetServerTimeRequest();
      final response = await _http.fire(request, showLoading: false);
      return response['Ticks'];
    } catch (_) {
      rethrow;
    }
  }
}

extension GetApplePayId on Api {
  Future getApplePayId(String id) async {
    try {
      final request = GetApplePayIdRequest();
      request.add('Id', id);
      final response = await _http.fire(request, showLoading: true);
      // debugPrint("GetHomeBanner-----:"+result.toString());
      return response;
    } catch (e) {
      rethrow;
    }
  }
}

extension UploadAppleVoucher on Api {
  Future uploadAppleVoucher(
      String id, String transactionId, String receipt) async {
    try {
      final request = UploadAppleVoucherRequest();
      request.add('OrderId', id);
      request.add('TransactionId', transactionId);
      request.add('Receipt', receipt);
      final response = await _http.fire(request, showLoading: true);
      // debugPrint("GetHomeBanner-----:"+result.toString());
      return response;
    } catch (e) {
      rethrow;
    }
  }
}

extension SearchApplePayStatus on Api {
  Future searchApplePayStatus(String id) async {
    try {
      final request = QueryApplePayStatus();
      request.add('Id', id);
      final response = await _http.fire(request, showLoading: true);
      // debugPrint("GetHomeBanner-----:"+result.toString());
      return response;
    } catch (e) {
      rethrow;
    }
  }
}

extension GetOssToken on Api {
  Future<Map> getOssToken() async {
    try {
      final request = OSSTokenRequest();
      request.add('Id', '4WE9LT68BhV2');
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

///商城支付凭证获取
extension GetShopPayToken on Api {
  Future<Map> getShopPayToken({required String orderId}) async {
    try {
      final request = ShopPayTokenRequest();
      request.add('OrderId', orderId);
      request.add('PayType', 'W06');
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

///支付宝支付
extension AliPay on Api {
  Future<Map> aliPay(
      {String? orderInfo, String? orderId, String? payType}) async {
    try {
      final request = AliPayRequest();
      request.add('OrderId', orderId ?? 'UNKONWN');
      request.add('PayType', payType ?? 'A02');
      final response = await _http.fire(request, showLoading: true);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

///刷新学员档案
extension RefreshStudentDoc on Api {
  Future<Map> refreshStudentDoc() async {
    try {
      final request = RefreshStudentDocRequest();
      final response = await _http.fire(request, showLoading: true);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

///查询学习证明上传状态
extension GetStudyProveUploadStatus on Api {
  Future<Map> getStudyProveUploadStatus({required int subjectId}) async {
    try {
      final request = GetStudyProveUploadStatusRequest();
      request.add('Subject', subjectId);
      final response = await _http.fire(request, showLoading: true);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

///注册推送
extension RegisterPush on Api {
  Future<Map> registerPushId({required String pushId}) async {
    try {
      final request = RegisterPushIdRequest();
      request.add('Category', Platform.isAndroid ? 2 : 1);
      request.add('PushId', pushId);
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

///查询推送数据
extension QueryPushData on Api {
  Future<Map> queryPushData() async {
    try {
      final request = QueryPushDataRequest();
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

///学车经验分享数据获取
extension GetExperienceShare on Api {
  Future<Map> getExperienceShareData(
      {required int subjectId, int? type}) async {
    try {
      final request = GetExperienceShareRequest();
      request.add('Subject', subjectId);
      request.add('type', type ?? 1);
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

///获取优惠卷
extension GetCouponData on Api {
  Future getCouponData(
      {required int index, required String status, required int row}) async {
    try {
      final request = GetCouponRequest();
      request.add('Index', index);
      request.add('Status', status);
      request.add('Rows', row);
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }

  Future receiveCoupon({
    required String uid,
  }) async {
    try {
      final request = ReceiveCouponRequest();
      request.add('Id', uid);
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

///获取banner轮播时间以及信息流广告刷新时间
extension BannerAndInformationAdRefreshTime on Api {
  Future<String> getBannerAndInformationAdRefreshTime() async {
    try {
      final request = GetBannerAndInformationAdRefreshTimeRequest();
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}


///刷新试看次数
extension SendVideoQuestionNotice on Api {
  Future<Map> sendVideoQuestionNotice() async {
    try {
      final request = SendVideoQuestionNoticeRequest();
      final response = await _http.fire(request, showLoading: false);
      return response;
    } catch (_) {
      rethrow;
    }
  }
}

//===================================计时相关===================================

extension Timing on Api {
  // 查询计时规则
  Future<Map<String, dynamic>> timingQuery() async {
    final request = TimingRulersRequest();
    final response = await _http.fire(request, showLoading: false);
    return response;
  }

  // /Student/Query/IsRecord
  // 查询学员是否已备案
  Future<Map<String, dynamic>> recordIsRecord() async {
    final request = RecordIsRecordRequest();
    final response = await _http.fire(request, showLoading: false);
    return response;
  }

  // 开始计时
  Future<Map> timingStart({
    required int Subject,
    required String TrainType,
    required int StartTicks,
    int StudyType = 3,
    int StudySubType = 1,
    List<Map> Photos = const [],
    String? TerminalModel,
    String? Terminal,
    String? StartExtend,
  }) async {
    final ip = await getIPAddress();
    final request = TimingStartRequest();
    request
      ..add('Subject', Subject) // 培训科目。
      ..add('TrainType', TrainType) // 培训车型	如：C1。
      ..add('StartTicks', StartTicks) // 开始培训时间	unixtimestamp。
      ..add('StudyType', StudyType) // 培训类型	1:课堂,2:模拟,3:远程。
      ..add('StudySubType', StudySubType) // 培训子类型	1:常规,2:从业培训,3:从业考试
      ..add('Channel', 'DX2') // 数据来源	大象驾到1.0(DX1)，大象驾到2.0(DX2)
      ..addIfNotNull('Photos',
          Photos) // 照片	需填写。示例：["http://www.baidu.com/1.jpg","http://www.baidu.com/2.jpg"]
      ..addIfNotNull('StartExtend', StartExtend)
      ..addIfNotNull('TerminalModel', TerminalModel) //终端型号	需填写。示例HUAWEI P60
      ..addIfNotNull(
          'Terminal', Terminal) // 终端类型	需填写：IOS(机型,版本)、ANDROID(机型,版本)、WEB(类型,版本)
      ..addIfNotNull('TerminalIp', ip); // 终端ip	需填写
    final response = await _http.fire(request, showLoading: false);
    return response;
  }

  // 结束计时
  Future<Map> timingEnd({
    required String Id,
    required int EndTicks,
    required int Periods,
    required int ValidatePeriods,
    required int FinishType,
    required int LastFinishTicks,
    Map? Photos = const {},
    String? EndExtend,
  }) async {
    final request = TimingEndRequest();
    request
      ..add('Id', Id) // 培训ID。
      ..add('EndTicks', EndTicks) // 结束计时时间	unixtimestamp。
      ..add('Periods', Periods) // 培训时长	单位：分钟。
      ..add('ValidatePeriods', ValidatePeriods) // 有效培训时长	单位：分钟。
      ..add('FinishType', FinishType) // 结束方式	1:正常结束,2:超时结束。
      ..add('LastFinishTicks', LastFinishTicks) // 结束时间	unixtimestamp。
      ..addIfNotNull('Photo',
          Photos) // 照片	需填写。示例：["http://www.baidu.com/1.jpg","http://www.baidu.com/2.jpg"]
      ..addIfNotNull('EndExtend', EndExtend); // 终端ip	需填写
    final response = await _http.fire(request, showLoading: false);
    return response;
  }

  // (辽宁国脉)上传计时照片，辽宁当地监管要求
  Future<Map> uploadImageToLiaoNing(
      {required int subject,
      required int startTicks,
      required int? photoTicks,
      required String? photoImgUrl,
      required int? signType}) async {
    final request = UploadImageToLiaoNingRequest();
    request
      ..add('Part', subject) // 科目
      ..add('BeginTime', startTicks) // 开始时间
      ..addIfNotNull('ShootingTime', photoTicks) // 照片时间
      ..addIfNotNull('PhotoUrl', photoImgUrl) // 照片地址
      ..addIfNotNull('SignType', signType); // 签到类型
    final response = await _http.fire(request, showLoading: false);
    return response;
  }

  // 上传计时照片
  Future<Map> timingUploadImageLog({
    required String Id,
    required Map Photo,
  }) async {
    final request = TimingPhotoUploadRequest();
    request
      ..add('Id', Id) // 培训ID。
      ..add('Photo', Photo); // 培训照片	参见照片模型。
    final response = await _http.fire(request, showLoading: false);
    return response;
  }

  // 上传计时心跳
  Future<Map> timingHeartbeat({
    required String Id,
  }) async {
    final request = TimingHeartRequest();
    request
      ..add('Id', Id) // 培训ID。
      ..add('Ticks', DateTime.now().millisecondsSinceEpoch); // 当前时间
    final response = await _http.fire(request, showLoading: false);
    return response;
  }

  // 查询计时状态
  Future<Map> timingQueryStatus(String id) async {
    final request = TimingStatusRequest();
    request.add('Id', id);
    final response = await _http.fire(request, showLoading: true);
    return response;
  }

  // 创建计时数据
  Future<Map> timingCreate({
    required String Channel,
    required String Terminal,
    required String TerminalModel,
    required String TrainType,
    required int StudyType,
    required int StudySubType,
    required int Subject,
    required int StartTicks,
    required int EndTicks,
    required int Periods,
    required int ValidatePeriods,
    List<String> Photos = const [],
    String? CoachId,
    String? VehicleId,
    String? DeviceId,
    String? Reserved,
    String? Remark,
    String? StartExtend,
    String? EndExtend,
  }) async {
    final ip = await getIPAddress();
    final request = TimingRecordCreateRequest();
    request
      ..add('Channel', Channel) // 数据来源	默认为：XYTH-APP。
      ..add('Terminal', Terminal) // 终端类型	需填写。终端设备类型 IOS,ANDROID,WEB...
      ..add('TerminalModel', TerminalModel) // 终端型号	终端型号
      ..addIfNotNull('TerminalIp', ip) // 终端ip
      ..add('TrainType', TrainType) // 培训车型	如：C1。
      ..add('StudyType', StudyType) // 培训类型	1:课堂,2:模拟,3:远程。
      ..add('StudySubType', StudySubType) // 培训子类型	1:常规,2:从业。
      ..add('Subject', Subject) // 科目	1~4。
      ..add('StartTicks', StartTicks) // 开始培训时间	unixtimestamp。
      ..addIfNotNull('CoachId', CoachId) // 教练ID
      ..addIfNotNull('VehicleId', VehicleId) // 车辆ID
      ..addIfNotNull('DeviceId', DeviceId) // 设备ID
      ..addIfNotNull('Reserved', Reserved) // 保留字段
      ..addIfNotNull('Remark', Remark) // 备注信息
      ..add('EndTicks', EndTicks) // 结束培训时间	unixtimestamp。
      ..add('Periods', Periods) // 计时培训时长	单位：分钟。
      ..add('ValidatePeriods', ValidatePeriods) // 有效计时培训时长	单位：分钟。
      ..addIfNotNull('Photos',
          Photos) // 照片	需填写。示例：["http://www.baidu.com/1.jpg","http://www.baidu.com/2.jpg"]
      ..addIfNotNull('StartExtend',
          StartExtend) // 签到扩展	{gdex:{stu:学员统一编号,code:签到/退信息,expire:有效截止时间}}
      ..addIfNotNull('EndExtend', EndExtend);
    final response = await _http.fire(request, showLoading: false);
    return response;
  }

  //查询未完成的计时数据
  Future<Map> timingQueryUnfinished() async {
    final request = TimingQueryUnfinishedRequest();
    request.add('Days', 3);
    List response = await _http.fire(request, showLoading: false);
    final res = response[0];
    // final response = await _http.fire(request, showLoading: false);

    return res;
  }

  //放弃计时
  Future<Map> timingDrop({required String Id}) async {
    final request = TimingDropRequest();
    request.add('Id', Id);
    final response = await _http.fire(request, showLoading: false);
    return response;
  }

  //学员人证合一验证
  Future<Map> studentVerify(
      {required String cardPhoto, required String perPhoto}) async {
    final request = StudentVerifyRequest();
    request
      ..add('CardPhotoBase64', cardPhoto)
      ..add('PerPhotoBase64', perPhoto);
    final response = await _http.fire(request, showLoading: false);
    return response;
  }

  //学员人证合一人工审核
  Future<Map> studentVerifyManual(
      {required String cardPhoto, required String perPhoto}) async {
    final request = StudentVerifyManualRequest();
    request
      ..add('CardPhotoBase64', cardPhoto)
      ..add('PerPhotoBase64', perPhoto);
    final response = await _http.fire(request, showLoading: false);
    return response;
  }
}

extension Face on Api {
  // 人脸质量检测 第一人脸质量	取值范围：0~100。
  Future<int> faceQualityCheck(String image) async {
    final request = FaceQualityRequest();
    request.add('ImageData', image);
    final response = await _http.fire(request, showLoading: false);
    if (response is Map && response['Score'] != null) {
      return response['Score'];
    } else {
      return 0;
    }
  }

  // 人脸AI检测 0:未活体, 1:活体, -1:未知错误, -2:没有检测到人脸信息。
  Future<int> faceAICheck(String image) async {
    final request = FaceAICheckRequest();
    request.add('ImageData', image);
    final response = await _http.fire(request, showLoading: false);
    if (response is Map && response['Score'] != null) {
      return response['Score'];
    } else {
      return 0;
    }
  }

  // 人脸比对 比对结果值	0~100。
  Future<int> faceCompare(String image) async {
    final request = FaceScoreRequest();
    request.add('ImageData', image);
    final response = await _http.fire(request, showLoading: false);
    if (response is Map && response['Score'] != null) {
      return response['Score'];
    } else {
      return 0;
    }
  }

  // 查询理论是否开通
  Future<bool> remoteOpenStatus() async {
    final request = RemoteOpenStatusRequest();
    final response = await _http.fire(request, showLoading: false);
    int status = response['Status'];
    return status == 1;
  }

  // 查询从业是否开通
  Future<bool> practiceOpenStatus() async {
    final request = PractionOpenStatusRequest();
    final response = await _http.fire(request, showLoading: false);
    int status = response['Status'];
    return status == 1;
  }
}

extension IpAddress on Api {
  Future<String?> getIPAddress() async {
    try {
      List<NetworkInterface> networkInterfaces = await NetworkInterface.list();
      for (NetworkInterface networkInterface in networkInterfaces) {
        for (InternetAddress address in networkInterface.addresses) {
          debugPrint(
              'IP address of ${networkInterface.name}: ${address.address}');
          return address.address;
        }
      }
      return null;
    } catch (_) {
      return null;
    }
  }
}

extension DXJD on Api {
  // 获取 dxjdToken
  Future<String> getDxjdToken({required String mobile}) async {
    final request = GetDxjdToken();
    request.add('Mobile', mobile);
    final response = await _http.fire(request, showLoading: false);
    final token = response['token'];
    return token;
  }

  // 续期 dxjdToken
  Future<String> renewDxjdToken(
      {required String mobile, required String token}) async {
    final request = RenewDxjdToken();
    request
      ..add('Mobile', mobile)
      ..add('Token', token);
    final response = await _http.fire(request, showLoading: false);
    return response['token'];
  }
}
