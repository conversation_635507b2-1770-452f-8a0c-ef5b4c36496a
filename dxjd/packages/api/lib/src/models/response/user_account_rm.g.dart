// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_account_rm.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserAccountRm _$UserAccountRmFromJson(Map<String, dynamic> json) =>
    UserAccountRm(
      uid: json['Uid'] as String,
      sid: json['Sid'] as String,
      name: json['Name'] as String?,
      bindMobile: json['BindMobile'] as String?,
      mobile: json['Mobile'] as String?,
      birth: (json['Birth'] as num?)?.toInt(),
      gender: (json['Gender'] as num?)?.toInt(),
      registerDivision: (json['RegisterDivision'] as num).toInt(),
      isBind: (json['IsBind'] as num).toInt(),
      platCode: (json['PlatCode'] as num).toInt(),
      realAuthstatus: (json['RealAuthStatus'] as num?)?.toInt(),
      isVip: (json['IsVip'] as num).toInt(),
      isRecorded: (json['IsRecorded'] as num).toInt(),
      image: json['Image'] as String?,
      platSchoolId: json['PlatSchoolId'] as String?,
      platSchoolName: json['PlatSchoolName'] as String?,
      platTrainType: json['PlatTrainType'] as String?,
      topicType: json['TopicType'] as String?,
      idCard: json['IdCard'] as String?,
      platRegisterDate: json['PlatRegisterDate'] as int?,
      registerDate: json['RegisterDate'] as int?,
          vip1ExpireValue: json['Vip1ExpireValue'] as int?,
          vip2ExpireValue: json['Vip2ExpireValue'] as int?,
          vip3ExpireValue: json['Vip3ExpireValue'] as int?,
          vip4ExpireValue: json['Vip4ExpireValue'] as int?,
    );

Map<String, dynamic> _$UserAccountRmToJson(UserAccountRm instance) =>
    <String, dynamic>{
      'Uid': instance.uid,
      'Sid': instance.sid,
      'Name': instance.name,
      'BindMobile': instance.bindMobile,
      'Mobile': instance.mobile,
      'Birth': instance.birth,
      'Gender': instance.gender,
      'RegisterDivision': instance.registerDivision,
      'IsBind': instance.isBind,
      'PlatCode': instance.platCode,
      'RealAuthStatus': instance.realAuthstatus,
      'IsVip': instance.isVip,
      'IsRecorded': instance.isRecorded,
      'Image': instance.image,
      'PlatSchoolId': instance.platSchoolId,
      'PlatSchoolName': instance.platSchoolName,
      'PlatTrainType': instance.platTrainType,
      'TopicType': instance.topicType,
      'IdCard': instance.idCard,
      'PlatRegisterDate': instance.platRegisterDate,
      'RegisterDate': instance.registerDate,
          'Vip1ExpireValue' : instance.vip1ExpireValue,
          'Vip2ExpireValue' : instance.vip2ExpireValue,
          'Vip3ExpireValue' : instance.vip3ExpireValue,
          'Vip4ExpireValue' : instance.vip4ExpireValue,
    };
